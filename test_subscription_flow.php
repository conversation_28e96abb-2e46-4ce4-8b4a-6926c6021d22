<!DOCTYPE html>
<html>
<head>
    <title>Test Subscription Flow</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script>
    <script src="resources/make_payment.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .plan-button { background: #007cba; color: white; border: none; }
        .disabled-btn { background: #ccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>Subscription Flow Test</h1>
    
    <?php
    session_start();
    
    // Check if user is logged in
    if (!isset($_SESSION['id'])) {
        echo "<p class='error'>❌ Please login first to test subscription flow</p>";
        echo "<a href='signup.php?login'>Login</a>";
        exit;
    }
    
    $user_id = $_SESSION['id'];
    echo "<p class='success'>✅ User logged in with ID: $user_id</p>";
    ?>
    
    <div class="test-section">
        <h3>1. Check Current Subscription Status</h3>
        <button onclick="checkSubscription()">Check Subscription</button>
        <div id="subscription-status"></div>
    </div>
    
    <div class="test-section">
        <h3>2. Test Plan Buttons</h3>
        <p>These buttons simulate the plan selection process:</p>
        
        <!-- Sample plan buttons -->
        <button class="plan-button" data-plan-id="1" data-plan-amount="1000" data-plan-duration="1 month">
            Basic Plan - ₦1,000/month
        </button>
        
        <button class="plan-button" data-plan-id="2" data-plan-amount="5000" data-plan-duration="6 months">
            Premium Plan - ₦5,000/6 months
        </button>
        
        <button class="plan-button" data-plan-id="3" data-plan-amount="10000" data-plan-duration="1 year">
            Pro Plan - ₦10,000/year
        </button>
        
        <div id="plan-test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. Direct Payment Test</h3>
        <p>Test payment function directly:</p>
        <button onclick="testDirectPayment()">Test Direct Payment</button>
        <div id="payment-test-result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. Console Logs</h3>
        <p>Check browser console (F12) for detailed logs during testing.</p>
    </div>

    <script>
        let userid = <?php echo $_SESSION['id']; ?>;
        let email = '<?php echo $_SESSION['email'] ?? '<EMAIL>'; ?>';
        
        console.log('Test page loaded for user:', userid, 'email:', email);
        
        function checkSubscription() {
            $('#subscription-status').html('<p class="info">Checking subscription...</p>');
            
            $.post("scripts/data-scripts/check-subscription.php", 
                { user_id: userid }, 
                function(data) {
                    console.log('Subscription check response:', data);
                    
                    let html = '<p><strong>Subscription Status:</strong></p>';
                    html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    if (data.has_subscription === true) {
                        html += '<p class="success">✅ User has active subscription</p>';
                    } else if (data.has_subscription === false) {
                        html += '<p class="info">ℹ️ User has no active subscription - can subscribe normally</p>';
                    } else {
                        html += '<p class="error">❌ Unexpected response format</p>';
                    }
                    
                    $('#subscription-status').html(html);
                }, 
                'json'
            ).fail(function(xhr, status, error) {
                console.error('Subscription check failed:', error);
                $('#subscription-status').html('<p class="error">❌ Failed to check subscription: ' + error + '</p>');
            });
        }
        
        function testDirectPayment() {
            $('#payment-test-result').html('<p class="info">Testing payment function...</p>');
            
            if (typeof plan_payment !== 'function') {
                $('#payment-test-result').html('<p class="error">❌ plan_payment function not found</p>');
                return;
            }
            
            console.log('Testing plan_payment function with test data');
            
            // Test with sample data (won't actually charge)
            try {
                // This will open Paystack modal but won't charge since it's test data
                plan_payment(100, 999, userid, email, 1);
                $('#payment-test-result').html('<p class="success">✅ Payment function called successfully (check for Paystack modal)</p>');
            } catch (error) {
                console.error('Payment function error:', error);
                $('#payment-test-result').html('<p class="error">❌ Payment function error: ' + error.message + '</p>');
            }
        }
        
        // Add click handlers to plan buttons
        $('.plan-button').on('click', function() {
            const planId = $(this).data('plan-id');
            const planAmount = $(this).data('plan-amount');
            const planDuration = $(this).data('plan-duration');
            
            console.log('Plan button clicked:', { planId, planAmount, planDuration });
            
            $('#plan-test-result').html('<p class="info">Testing plan selection...</p>');
            
            // Check subscription status first
            $.post("scripts/data-scripts/check-subscription.php", 
                { user_id: userid }, 
                function(subscription_data) {
                    console.log('Subscription check for plan selection:', subscription_data);
                    
                    if (subscription_data.has_subscription === true) {
                        $('#plan-test-result').html('<p class="info">ℹ️ User has subscription - would show change plan modal</p>');
                    } else {
                        $('#plan-test-result').html('<p class="success">✅ User has no subscription - proceeding with payment</p>');
                        
                        // Extract month count
                        let monthCount = 1;
                        if (planDuration.toLowerCase().includes('month')) {
                            monthCount = parseInt(planDuration) || 1;
                        } else if (planDuration.toLowerCase().includes('year')) {
                            monthCount = 12;
                        }
                        
                        console.log('Would call plan_payment with:', {
                            planAmount, planId, userid, email, monthCount
                        });
                        
                        // Uncomment to actually test payment:
                        // plan_payment(planAmount, planId, userid, email, monthCount);
                    }
                }, 
                'json'
            ).fail(function() {
                $('#plan-test-result').html('<p class="error">❌ Failed to check subscription status</p>');
            });
        });
        
        // Auto-check subscription on page load
        $(document).ready(function() {
            checkSubscription();
        });
    </script>
</body>
</html>
