$(document).ready(function(){
    // console.log("Search Ready");

    $("#searchVal").keyup(function(){

        if($("#searchVal").val() == ""){
            
            $(".inpsuglist").hide();
            
        }else if($("#searchVal").val().length > 1){
            
            //$(".inpsuglist").show();
            // console.log("Typing Search");
            $(".ul_inpsuglist").empty();

            let searchInp = $("#searchVal").val();
            let specChar = searchInp.charAt(0);
            let newSearchInp = searchInp.slice(1);

            if(specChar == "@"){
                // console.log("Searching Username");
                // console.log(newSearchInp);
                // console.log(newSearchInp.length);

                $.ajax({
                    type: "POST",
                    url: 'scripts/data-scripts/search.data.php',       
                    data: jQuery.param({ keyword_at: newSearchInp }) ,
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',

                    success: function(response)
                    {
                        var jsonData = JSON.parse(response);                        
                        var results = jsonData.results;

                        if(results.length < 1){
                            $(".inpsuglist").hide();

                        }else{

                            results.forEach(user =>{
                                $(".inpsuglist").show();
    
                                $(".ul_inpsuglist").append(`<li><a href='#'>${user.username}</a></li>`);
    
    
                                // console.log(user);
    
                            });

                        }
                      
                    }
                });


            }else if(specChar == "#"){
                console.log("Searching Tags");
                console.log(newSearchInp);

                $.ajax({
                    type: "POST",
                    url: 'scripts/data-scripts/search.data.php',       
                    data: jQuery.param({ keyword_hash: newSearchInp }) ,
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',

                    success: function(response)
                    {
                        var jsonData = JSON.parse(response);                        
                        var results = jsonData.results;

                        if(results.length < 1){
                            $(".inpsuglist").hide();

                        }else{
                            // console.log(results);

                            results.forEach(tag =>{
                                $(".inpsuglist").show();
    
                                $(".ul_inpsuglist").append(`<li>${tag.tags}</li>`);
    
    
                                // console.log(tag);
    
                            });

                        }
                      
                    }
                });

            }else{
                console.log("Searching Photo Title");
                // console.log(searchInp);

                $.ajax({
                    type: "POST",
                    url: 'scripts/data-scripts/search.data.php',       
                    data: jQuery.param({ keyword: searchInp }) ,
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',

                    success: function(response)
                    {
                        var jsonData = JSON.parse(response);                        
                        var results = jsonData.results;

                        if(results.length < 1){
                            $(".inpsuglist").hide();

                        }else{

                            // console.log(results);

                            results.forEach(all =>{
                                $(".inpsuglist").show();
    
                                $(".ul_inpsuglist").append(`<li>${all.title}</li>`);
    
    
                                // console.log(user);
    
                            });

                        }
                      
                    }
                });

            }

        }


    }).delay(2000);

    $("#search-form").submit(function(e){
        e.preventDefault();


        $("#searchBtn").click(function(){
            let searchInp = $("#searchVal").val();

            let specChar = searchInp.charAt(0);

            let newSearchInp = searchInp.slice(1);
    
            // console.log(searchInp);

            // console.log(specChar);
    
            // console.log(newSearchInp);
    
        });



    });
    

});