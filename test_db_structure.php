<?php
include 'resources/db_config/db_connection.php';

echo "<h2>Database Structure Test</h2>";

// Test connection
if ($db->connect_error) {
    die("Connection failed: " . $db->connect_error);
} else {
    echo "<p>✅ Database connection successful</p>";
}

// Check if purchase_history table exists
$tables_to_check = ['purchase_history', 'puchase_history'];

foreach ($tables_to_check as $table_name) {
    echo "<h3>Checking table: $table_name</h3>";

    $result = $db->query("SHOW TABLES LIKE '$table_name'");
    if ($result->num_rows > 0) {
        echo "<p>✅ Table '$table_name' exists</p>";

        // Get table structure
        $structure = $db->query("DESCRIBE $table_name");
        if ($structure) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }

        // Get sample data
        $sample = $db->query("SELECT * FROM $table_name LIMIT 5");
        if ($sample && $sample->num_rows > 0) {
            echo "<p><strong>Sample data (first 5 rows):</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";

            // Get column names
            $fields = $sample->fetch_fields();
            echo "<tr>";
            foreach ($fields as $field) {
                echo "<th>" . $field->name . "</th>";
            }
            echo "</tr>";

            // Reset result pointer
            $sample->data_seek(0);
            while ($row = $sample->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ Table '$table_name' is empty</p>";
        }
    } else {
        echo "<p>❌ Table '$table_name' does not exist</p>";
    }
    echo "<hr>";
}

// Check user_plans table
echo "<h3>Checking table: user_plans</h3>";
$result = $db->query("SHOW TABLES LIKE 'user_plans'");
if ($result->num_rows > 0) {
    echo "<p>✅ Table 'user_plans' exists</p>";

    // Get table structure
    $structure = $db->query("DESCRIBE user_plans");
    if ($structure) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ Table 'user_plans' does not exist</p>";
}

// Check session data
session_start();
echo "<h3>Session Information</h3>";
if (isset($_SESSION['id'])) {
    echo "<p>✅ User ID in session: " . $_SESSION['id'] . "</p>";
} else {
    echo "<p>❌ No user ID in session</p>";
}

if (isset($_SESSION['username'])) {
    echo "<p>✅ Username in session: " . $_SESSION['username'] . "</p>";
} else {
    echo "<p>❌ No username in session</p>";
}

if (isset($_SESSION['cart'])) {
    echo "<p>✅ Cart in session with " . count($_SESSION['cart']) . " items</p>";
    if (count($_SESSION['cart']) > 0) {
        echo "<pre>" . print_r($_SESSION['cart'], true) . "</pre>";
    }
} else {
    echo "<p>❌ No cart in session</p>";
}

$db->close();
