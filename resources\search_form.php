<div class="search-form">
    <div class="container">
        <div class="image-credit">
            <div>Image Credit: <span><a href="#" id="image_credit"><PERSON>ew<PERSON><PERSON><PERSON></a></span></div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <?php
                include_once dirname(__DIR__) . '/scripts/class-scripts/search.class.php';

                $searchObj = new Search();


                ?>
                <form id="search-form" name="gs" method="submit" role="search" action="#">
                    <div class="row">
                        <div class="col-lg-8">
                            <fieldset>
                                <div class="search-wrapper cf">
                                    <input type="text" id="searchVal" name="searchTitle" class="searchText" placeholder="Search @username, #tag, photo..." autocomplete="on" required>
                                    
                                </div>
                                <div class="inpsuglist">
                                    <ul class="ul_inpsuglist">
                                       
                                    </ul>
                                </div>
                            </fieldset>
                        </div>
                        <div class="col-lg-2">
                            <fieldset>
                                <select name="Plan" class="form-select" aria-label="Default select example" id="chooseCategory" onChange="this.form.click()">

                                    <option selected value="Images"> Images</option>



                                    <option value="Standard">Illustrations</option>
                                    <option value="Professional">Vectors</option>
                                    <option value="Golden">Golden</option>
                                    <option value="Premium">Premium</option>
                                </select>
                            </fieldset>
                        </div>
                        <div class="col-lg-2">
                            <fieldset>
                                <select name="category1" class="form-select" aria-label="Default select example" id="chooseCategory" onChange="this.form.click()">
                                    <option selected>Categories</option>
                                    <?php
                                    $categories = $searchObj->fetch_categories();

                                    foreach ($categories as $category) {

                                    ?>
                                        <option value="<?= $category['id'] ?>"><?= $category['name'] ?></option>

                                    <?php

                                    }

                                    ?>

                                </select>
                            </fieldset>
                            <button class="main-button" id="searchBtn1">Search</button>
                        </div>
                        <div class="col-lg-12 text-center trending-list">
                            <span><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 576 512">
                                    <path d="M384 160c-17.7 0-32-14.3-32-32s14.3-32 32-32H544c17.7 0 32 14.3 32 32V288c0 17.7-14.3 32-32 32s-32-14.3-32-32V205.3L342.6 374.6c-12.5 12.5-32.8 12.5-45.3 0L192 269.3 54.6 406.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l160-160c12.5-12.5 32.8-12.5 45.3 0L320 306.7 466.7 160H384z" />
                                </svg> Trending : &nbsp;&nbsp;</span>
                            <span class="badge text-bg-dark">Valentines Day</span>
                            <span class="badge text-bg-dark">Zoo</span>
                            <span class="badge text-bg-dark">Dancing</span>
                            <span class="badge text-bg-dark">Celebration</span>
                            <span class="badge text-bg-dark">Ocean</span>
                        </div>
                        <!-- <div class="col-lg-12">
                            <fieldset>
                                <button class="main-button" id="searchBtn">Search Now</button>
                            </fieldset>
                        </div> -->
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>