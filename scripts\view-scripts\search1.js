$(document).ready(function(){

    let isSearchMode = false;
    let currentFilters = {
        search: "",
        imageSelected: 'images',
        category: ""
    };

    $("#searchBtn1").on('click', function(e){
        e.preventDefault();
        isSearchMode = true;
        let search = $("#searchVal").val();
        let selectedImage;
        $(select[name="Plan"]).on('change', function(){
            selectedImage = $(this).val();
        });
        let selectedCategory;
        $(select[name="category1"]).on('change', function(){
            selectedCategory = $(this).val();
        });

        currentFilters = {
            search: search,
            imageSelected: selectedImage,
            selectedCategory: selectedCategory
        };

        fetchFilter();

        
       
    })

    function fetchFilter(){
        $.ajax({
            url: 'scripts/data-scripts/search.data.php',
            data: {
                search: currentFilters.search,
                imageSelected: currentFilters.imageSelected,
                category: currentFilters.category,
            },
            dataType: 'json',
            success: function(response){
                if(response.status == "success"){
                    
                }
            }
        })
    }
})